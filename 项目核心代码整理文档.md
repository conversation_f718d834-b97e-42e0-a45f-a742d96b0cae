# 智能手表项目核心代码整理文档

## 项目概述

本项目是一个基于CH579芯片的智能手表系统，集成了辐射剂量检测、心率血氧监测、GPS定位、4G通信等功能。项目采用LVGL图形库构建用户界面，使用EC800M模块进行4G通信，支持MQTT协议数据上传。

## 项目架构

```
lyls-00021/
├── src/                    # 源代码目录
│   ├── main.c             # 主程序入口
│   ├── sys_it.c           # 中断服务程序
│   ├── system_set.c       # 系统配置管理
│   ├── diver/             # 驱动程序目录
│   ├── lvgl_user/         # LVGL用户界面
│   └── nanopb_user/       # Protobuf数据协议
├── lib/                   # 库文件目录
│   ├── CH579_Lib/         # CH579芯片库
│   ├── lvgl/              # LVGL图形库
│   └── nanopb/            # Nanopb协议库
└── obj/                   # 编译输出目录
```

## 1. 主程序模块 (main.c)

### 1.1 系统初始化流程

```c
int main()
{   
    system_wdog_init();                    // 看门狗初始化
    
    init_userkey();                        // 按键初始化
    if(R8_GLOB_RESET_KEEP == 0)waite_power_on_event();  // 等待开机事件
    system_state.watch_power_state = 1;
    
    // 外设初始化
    tmdelay_ms_init();                     // 延时定时器初始化
    uart0_init();                          // UART0初始化(EC800M通信)
    uart1_init();                          // UART1初始化(调试串口)
    uart2_init();                          // UART2初始化
    spi_flash_init();                      // SPI Flash初始化
    io_i2c_io_init();                      // I2C接口初始化
    user_sys_adc_init();                   // ADC初始化
    
    system_wdog_feed(0);
    pm_init();                             // 屏幕初始化
    pm_fill_color(0);                      // 屏幕填充
    system_wdog_feed(0);
    
    read_system_settings();                // 读取系统配置
    
    // LVGL图形库初始化
    lv_init();
    lv_port_disp_init();                   // 显示接口初始化
    lv_port_indev_init();                  // 输入设备初始化
    ui_init();                             // UI界面初始化
    
    // CSI传感器初始化
    csi_sensor_int();
    csi_sensor_collect_enable(ENABLE);
    system_wdog_feed(0);
    
    // 主循环
    while(1){
        user_display_updata();             // 屏幕与显示量的更新
        lv_task_handler();                 // LVGL库调度任务
        adc_scan_channel_task(...);        // ADC扫描任务
        at_state_machine_run(&default_ec800m_mc);  // DTU模块运行任务
        battery_reflash_task();            // 电池状态刷新
        auto_save_task();                  // 自动保存任务
        system_wdog_feed(0);               // 喂狗
    }   
}
```

### 1.2 主要功能说明

- **系统初始化**: 完成硬件外设、通信接口、图形库的初始化
- **主循环**: 采用轮询方式处理各个功能模块的任务
- **看门狗**: 防止系统死机，定期喂狗保证系统稳定运行

## 2. 中断服务程序 (sys_it.c)

### 2.1 定时器中断

```c
void TMR0_IRQHandler(void)        
{
    if(TMR0_GetITFlag(TMR0_3_IT_CYC_END))
    {
        TMR0_ClearITFlag(TMR0_3_IT_CYC_END);   
        
        uart_check_rxd_finish();          // UART接收完成检查
        tmdelay_tick_inc();                // 延时计数器递增
        lv_tick_inc(1);                    // LVGL时钟递增
    }
}
```

### 2.2 GPIO中断

```c
void GPIO_IRQHandler(void)
{
    chsc5816_tp_interrupt_handler();      // 触摸屏中断处理
    
    if(GPIOB_ReadITFlagBit(GPIO_Pin_14)){
        GPIOB_ClearITFlagBit(GPIO_Pin_14); // 清中断，PB14
        drc_pulse_interrupt_handler(&csi_sensor);  // CSI脉冲计数中断
    }
}
```

### 2.3 RTC中断

```c
void RTC_IRQHandler(void)    // 秒中断
{
    if(RTC_GetITFlag(RTC_TMR_EVENT)){
        RTC_ClearITFlag(RTC_TMR_EVENT);
        drc_second_interrupt_handler(&csi_sensor);  // CSI传感器秒中断处理
    }
}
```

## 3. 系统配置管理 (system_set.c)

### 3.1 系统配置结构体

```c
sys_set_t system_setings = {
    .sn = "LYWB01_A00000",                 // 设备序列号
    .param_hash = "default",               // 参数哈希值
    
    .bt_switch = 0,                        // 蓝牙开关
    .fly_mode_switch = 0,                  // 飞行模式开关
    .gnss_switch = 1,                      // GNSS开关
    .auto_upsend_switch = 1,               // 自动上传开关
    .auto_upsend_interval_envent = DEFAULT_SHORT_UPSEND_INTERVAL,
    .auto_upsend_interval_normal = DEFAULT_LONG_UPSEND_INTERVAL,
    .dose_rate_alarm_threshold = 2.0f,     // 剂量率报警阈值
    
    // CSI传感器拟合参数
    .csi_dr_fitting_param = {
        {0, 6.8f, 0, 0.3235f, 0},
        {6.8, 21.7f, 0, 0.442f, -0.8f},
        {21.7f, 92.0f, 0, 0.4523f, -1.0159f},
        {92.0f, 186.6f, 0, 0.463f, -1.996f},
        {186.6f, 634.0f, 0, 0.303f, 27.8f},
    },
    .csi_dr_fitting_param_num = 5,
    .csi_alarm_dose_rate_max = 60.0f,      // CSI报警剂量率上限
    .csi_alarm_dose_rate_min = 0,          // CSI报警剂量率下限
    .csi_alarm_dose_rate_sum = 0,          // CSI累计剂量报警阈值
};
```

### 3.2 配置读取与保存

```c
void read_system_settings(void)
{
    sys_setting_save_t read_save_t;
    spi_flash_read_data(0x00, (uint8_t *)&read_save_t, sizeof(sys_setting_save_t));
    
    if(read_save_t.init_flag != SYS_SETTING_SAVE_INIT_FLAG){
        system_settings_saved();          // 初始化保存
    }
    else{
        // 校验和检查，选择有效配置
        uint16_t main_check_sum = 0;
        uint16_t backup_check_sum = 0;
        // ... 校验逻辑
        memcpy(&system_setings, effective_seting, sizeof(sys_set_t));
    }
}
```

## 4. 驱动程序模块

### 4.1 CSI传感器驱动 (csi_sensor.c)

CSI传感器用于辐射剂量检测，是项目的核心功能之一。

```c
// CSI传感器对象
drc_sensor_obj_t csi_sensor;

// 传感器初始化
void csi_sensor_int(void)
{
    memset(&csi_sensor, 0, sizeof(csi_sensor));
    
    // 注册API接口
    csi_sensor.api.pin_init = csi_sensor_pin_init;
    csi_sensor.api.second_interrupt_init = urtc_second_irq_switch;
    csi_sensor.api.sensor_pw_switch = csi_sensor_power;
    
    // 设置拟合与报警参数
    memcpy(csi_sensor.fitting_param, system_setings.csi_dr_fitting_param, 
           sizeof(piecewise_fitting_param_t) * system_setings.csi_dr_fitting_param_num);
    csi_sensor.fitting_param_num = system_setings.csi_dr_fitting_param_num;
    csi_sensor.alarm_param_max_dose_rate = system_setings.csi_alarm_dose_rate_max;
    
    // 设置跳变检测算法参数
    drc_set_jpavg_param(&csi_sensor, 3.0f, 10.0f, 3, 10, 10);
}

// 电源控制
void csi_sensor_power(uint8_t en)
{
    if(en){
        GPIOB_SetBits(GPIO_Pin_13);        // 开启电源
    }
    else{
        GPIOB_ResetBits(GPIO_Pin_13);      // 关闭电源
    }
}
```

### 4.2 剂量率计数器 (dose_rate_counter.c)

实现辐射剂量的计算和跳变检测算法。

```c
// 脉冲中断处理函数
void drc_pulse_interrupt_handler(drc_sensor_obj_t * obj)
{
    obj->cnt_temp++;                       // 脉冲计数累加
}

// 秒中断处理函数
void drc_second_interrupt_handler(drc_sensor_obj_t *obj)
{
    obj->cnt_now = obj->cnt_temp;          // 获取当前脉冲计数值
    obj->cnt_temp = 0;                     // 清零，得到每秒计数值CPS
    
    if(obj->power_statu){                  // 传感器开启状态
        if(obj->re_start){                 // 丢弃第一秒数据
            obj->re_start = 0;					
        }   
        else{
            // 跳变检测
            uint8_t pulse_jump_flag = JPAVG_AddDataToBuffer(obj->cnt_now, &obj->jump_averager);
            obj->cps = JPAVG_GetDataStableAverage(&obj->jump_averager);
            
            if(pulse_jump_flag != 0){      // 有跳变
                obj->re_start = 1;
                JPAVG_DataBufferClean(&obj->jump_averager);
            }  
            else{                          // 无跳变，进行剂量率计算
                float x = obj->cps;
                uint32_t index = 0;
                
                // 查找匹配的拟合参数区间
                for(index = 0; index < obj->fitting_param_num; index++){
                    if((x >= obj->fitting_param[index].cps_start) && 
                       (x <= obj->fitting_param[index].cps_end)){
                        break;
                    }
                }
                
                // 二次拟合计算剂量率
                float a = obj->fitting_param[index].param_a;
                float b = obj->fitting_param[index].param_b;
                float c = obj->fitting_param[index].param_c;
                obj->dose_rate = x * x * a + x * b + c;
                
                // 报警检测
                obj->alarm = 0;
                if((obj->alarm_param_max_dose_rate > 0) && 
                   (obj->dose_rate >= obj->alarm_param_max_dose_rate)){
                    obj->alarm |= (1 << 1);    // 剂量率过高报警
                }
                
                // 累计剂量计算
                if(JPAVG_GetStableFiledNum(&obj->jump_averager) > 
                   obj->jump_averager.jump_check_start_data_num){
                    obj->dose_sum += obj->dose_rate/3600;  // 累计剂量
                }
            }
        }
    }
}
```

### 4.3 跳变检测算法 (jump_averager.c)

实现基于统计学的跳变检测算法，用于识别辐射环境的突变。

```c
// 添加数据到缓存并进行跳变检测
uint8_t JPAVG_AddDataToBuffer(DATA_TYPE data, jump_averager_obj_t * obj)
{
    if(obj->last_data_buffer_filed_num >= obj->param_out_cgm_1_t){
        // 将"最近数组"中最老的数据填入原始数据缓存
        DATA_TYPE save_data = obj->last_data_buffer[obj->last_data_buffer_index];
        obj->data_buffer_sum -= obj->data_buffer[obj->data_buffer_index];
        obj->data_buffer[obj->data_buffer_index] = save_data;

        // 更新统计参数
        obj->data_buffer_sum += save_data;
        obj->stable_data_average = obj->data_buffer_sum / obj->data_buffer_filed_num;

        // 计算方差
        double variance_sum = 0;
        for(uint32_t i = 0; i < obj->data_buffer_filed_num; i++){
            double diff = obj->data_buffer[i] - obj->stable_data_average;
            variance_sum += diff * diff;
        }
        obj->stable_data_average_variance = sqrt(variance_sum / obj->data_buffer_filed_num);

        if(obj->data_buffer_filed_num >= obj->jump_check_start_data_num){
            double difference = fabs(data - obj->stable_data_average);
            double n_cgm_th_1 = obj->param_cgm_1 * obj->stable_data_average_variance;  // 精确阈值
            double n_cgm_th_2 = obj->param_cgm_2 * obj->stable_data_average_variance;  // 粗阈值

            // 3σ跳变检测
            if(difference >= n_cgm_th_2){
                obj->jump_flag = 1;        // 粗阈值跳变
            }
            else if(difference >= n_cgm_th_1){
                obj->out_difference_times++;
                if(obj->out_difference_times >= obj->param_out_cgm_1_t){
                    obj->jump_flag = 2;    // 连续超出精确阈值
                }
            }
            else{
                obj->out_difference_times = 0;
            }

            // 持续增减法检测
            if((obj->stable_data_average - obj->last_data_average) > 0.001f){
                obj->increase_times++;
                obj->decrease_times = 0;
            }
            else if((obj->last_data_average - obj->stable_data_average) > 0.001f){
                obj->decrease_times++;
                obj->increase_times = 0;
            }

            // 连续变化检测
            if((obj->increase_times >= obj->param_ct_inodc_t) ||
               (obj->decrease_times >= obj->param_ct_inodc_t)){
                obj->jump_flag = 3;        // 连续增减跳变
            }
        }
    }

    // 更新最近数据缓存
    obj->last_data_buffer[obj->last_data_buffer_index] = data;
    obj->last_data_buffer_index = (obj->last_data_buffer_index + 1) % LAST_DATA_BUFFER_LEN;

    uint8_t return_flag = obj->jump_flag;
    obj->jump_flag = 0;
    return return_flag;
}
```

### 4.4 电池管理 (battery.c)

实现电池电量检测和充电状态监控。

```c
// 电池电量检测
uint8_t get_battery(uint8_t * charger)
{
    if(!sys_adc_channel_table[0].result_ready) return 0;

    // 从ADC读取电压值
    float dev_voltage = sys_adc_channel_table[0].voltage;

    // 计算实际电池电压 (电阻分压: 100k/(100k+30k))
    float bt_voltage = (dev_voltage / 100000) * 130000;

    // 电池电量百分比计算 (2.8V-4.1V)
    int8_t battery_prt = ((bt_voltage - 2.8f) / (4.1f - 2.8f)) * 100;
    battery_prt = battery_prt > 100 ? 100 : battery_prt;
    battery_prt = battery_prt < 0 ? 0 : battery_prt;

    // 电量变化平滑处理
    static uint8_t last_prt = 0xff;
    static uint8_t zhong_jian_prt = 0xff;
    static uint32_t changed_delay = 0;

    if(last_prt == 0xff){
        last_prt = battery_prt;
        zhong_jian_prt = battery_prt;
    }

    if(last_prt != battery_prt){
        changed_delay++;
    }
    else{
        changed_delay = 0;
    }

    // 持续变化100次后更新电量值
    if(changed_delay >= 100){
        last_prt = zhong_jian_prt;
        changed_delay = 0;
    }
    else if(changed_delay == 50){
        zhong_jian_prt = battery_prt;
    }

    // 充电器状态检测
    *charger = 0;
    GPIOA_ModeCfg(GPIO_Pin_10, GPIO_ModeIN_Floating);
    if(GPIOA_ReadPortPin(GPIO_Pin_10)){
        *charger = 1;  // 充电器已插入
    }

    return last_prt;
}
```

### 4.5 按键处理 (userkey.c)

实现按键的短按、长按检测。

```c
// 按键扫描函数
uint8_t sacn_userkey(void)
{
    static uint32_t t = 0;
    static uint8_t step = 0;             // 状态机: 0-检测按下; 1-检测松手; 2-返回事件
    static uint32_t t_continue = 0;      // 长按计时器

    // 长按检测
    if(get_userkey() == 0){              // 按键按下
        if(t_continue++ >= PRESSED_LONG_TIME){
            step = 0;
            t = 0;
            t_continue = 0;
            return 2;                    // 返回长按事件
        }
    }
    else{
        t_continue = 0;
    }

    // 短按检测状态机
    switch (step)
    {
        case 0: // 监测是否按下
        {
            if(get_userkey() == 0){
                t++;
            }
            else{
                t = 0;
            }

            if(t >= PRESSED_SHORT_TIME){
                t = 0;
                step = 1;                // 进入检测松手状态
            }
        }
        break;

        case 1: // 监测是否松手
        {
            if(get_userkey() == 1){
                t++;
            }
            else{
                t = 0;
            }

            if(t >= 100){
                t = 0;
                step = 2;                // 进入返回事件状态
            }
        }
        break;

        case 2: // 返回按键事件
        {
            step = 0;
            return 1;                    // 返回短按事件
        }

        default:
            break;
    }

    return 0;                            // 无事件
}
```

## 5. 通信模块

### 5.1 EC800M 4G通信模块 (ec800m.c)

实现4G网络通信和MQTT数据上传功能。

```c
// EC800M状态枚举
enum ec800m_state
{
    // 初始化状态序列
    POWER_OFF_ing, POWER_OFF, POWER_ON_ing, REDY,
    GET_IMEI, GET_ICCID, SET_CREG, SET_GPS_APFLASH,
    CEK_AGPS, SET_AGPS, OPEN_GPS, MQTT_COFIG,
    MQTT_OPEN, MQTT_CONNECT, MQTT_SUB,

    // 空闲任务
    IDEL,

    // 事件触发状态
    GET_CREG, GET_TIME, GET_CSQ, GET_GNSS,
    MQTT_REQUEST_PUB, MQTT_PUB_HEX,
};

// EC800M消息结构体
typedef struct
{
    uint8_t upsend_state;                // 上报状态: 0-空闲; 1-正在上报; 2-成功; 3-失败
    uint8_t new_data_redy;               // 上报数据就绪标志
    uint8_t * new_data;                  // 需要上报的数据
    uint16_t new_data_length;            // 数据长度
    unix_time_t last_upsend_time;        // 上次上报时间
    uint8_t imei[16];                    // 设备IMEI
    uint8_t iccid[21];                   // SIM卡ICCID
    uint16_t lac;                        // 位置区码
    uint32_t cid;                        // 小区ID
    uint8_t csq;                         // 信号强度
    uint8_t gnss_star_num;               // 卫星数量
    double gnss_latitude;                // 纬度
    double gnss_longitude;               // 经度
    uint16_t gnss_altitude;              // 海拔
}ec800m_msg_t;

// MQTT发布请求
uint8_t _request_pub(uint8_t * next_state)
{
    uint8_t mqtt_pub_cmd[90];
    memset(mqtt_pub_cmd, 0, sizeof(mqtt_pub_cmd));
    sprintf((char *)mqtt_pub_cmd, "AT+QMTPUBEX=0,0,0,0,\"/iot/%s/pb/datapoint/%s\",%d\r\n",
            MQTT_DEVICE_ID, default_ec800m.imei, default_ec800m.new_data_length);
    default_ec800m_mc.command_send(mqtt_pub_cmd, strlen((char *)mqtt_pub_cmd));
    default_ec800m.upsend_state = 1;
    return 0;
}

// 空闲事件检查
uint8_t _idel_check_envent(uint8_t * next_state)
{
    static uint32_t get_time_time = 0;
    static uint32_t get_creg_time = 0;
    static uint32_t get_csq_time = 0;
    static uint32_t get_gnss_time = 0;
    static unix_time_t idle_time = 0;

    // 定期获取网络时间
    if((GET_MS_TICK() - get_time_time >= 3600000) || (get_time_time == 0)){
        get_time_time = GET_MS_TICK();
        * next_state = GET_TIME;
        return 1;
    }

    // 定期获取网络注册状态
    if((GET_MS_TICK() - get_creg_time >= 60000) || (get_creg_time == 0)){
        get_creg_time = GET_MS_TICK();
        * next_state = GET_CREG;
        return 1;
    }

    // 定期获取信号强度
    if((GET_MS_TICK() - get_csq_time >= 30000) || (get_csq_time == 0)){
        get_csq_time = GET_MS_TICK();
        * next_state = GET_CSQ;
        return 1;
    }

    // 定期获取GNSS定位
    if((GET_MS_TICK() - get_gnss_time >= 5000) || (get_gnss_time == 0)){
        get_gnss_time = GET_MS_TICK();
        * next_state = GET_GNSS;
        return 1;
    }

    // 检查数据上传事件
    check_upsend_envent();
    if(default_ec800m.new_data_redy){
        * next_state = MQTT_REQUEST_PUB;
        idle_time = 0;
        return 1;
    }

    // 空闲超时关机
    if(idle_time == 0){
        idle_time = sys_unix_time;
    }
    else if((sys_unix_time - idle_time) >= 180){
        * next_state = POWER_OFF_ing;
        idle_time = 0;
        return 1;
    }

    // 飞行模式检查
    if(system_setings.fly_mode_switch){
        * next_state = POWER_OFF_ing;
        return 1;
    }

    return 0;
}
```

### 5.2 AT状态机 (at_state_machine.c)

实现AT命令的状态机管理。

```c
// 状态机运行函数
void at_state_machine_run(at_state_machine_t * state_machine)
{
    uint8_t now_statu = state_machine->now_state;
    static uint16_t last_statu = 0xFFFF;

    // 状态切换时清理临时变量
    if(last_statu != now_statu && last_statu != 0xFFFF){
        state_machine->state_table[last_statu].re_try_times_temp = 0;
        state_machine->state_table[last_statu].time_temp = 0;
    }
    last_statu = now_statu;

    // URC消息检查
    if(*state_machine->rxd_message_finished == 1){
        for(uint16_t i = 0; i < state_machine->urc_care_table_length; i++){
            if(strstr((char*)state_machine->rxd_message_buffer,
                     (char*)state_machine->urc_care_table[i].urc_key) != NULL){
                uint8_t next_state;
                if(state_machine->urc_care_table[i].urc_callback(
                   state_machine->rxd_message_buffer, &next_state)){
                    state_machine->now_state = next_state;
                    _clear_state_machine_rxd_buffer(state_machine);
                    return;
                }
            }
        }
    }

    // 应答检查
    if(*state_machine->rxd_message_finished == 1){
        if(state_machine->state_table[now_statu].wait_ack != NULL){
            if(strstr((char*)state_machine->rxd_message_buffer,
                     (char*)state_machine->state_table[now_statu].wait_ack) != NULL){
                if(state_machine->state_table[now_statu].ack_callback != NULL){
                    uint8_t next_state;
                    if(state_machine->state_table[now_statu].ack_callback(
                       state_machine->rxd_message_buffer, &next_state)){
                        state_machine->now_state = next_state;
                        _clear_state_machine_rxd_buffer(state_machine);
                        return;
                    }
                }
            }
        }
    }

    // 动作发起和超时处理
    if(0 == state_machine->state_table[now_statu].time_temp){
        if(state_machine->state_table[now_statu].onece_time_out != NO_TIMEOUT){
            state_machine->state_table[now_statu].time_temp = GET_MS_TICK();
        }

        // 超时检查
        if(state_machine->state_table[now_statu].re_try_times ==
           state_machine->state_table[now_statu].re_try_times_temp){
            if(state_machine->state_table[now_statu].timeout_callback != NULL){
                uint8_t next_state;
                if(state_machine->state_table[now_statu].timeout_callback(&next_state)){
                    state_machine->now_state = next_state;
                    _clear_state_machine_rxd_buffer(state_machine);
                    return;
                }
            }
        }
        else{
            // 发送AT命令
            if(state_machine->state_table[now_statu].at_cmd != NULL){
                state_machine->command_send(state_machine->state_table[now_statu].at_cmd,
                                          strlen((char*)state_machine->state_table[now_statu].at_cmd));
            }

            // 执行动作回调
            if(state_machine->state_table[now_statu].action_callback != NULL){
                uint8_t next_state;
                if(state_machine->state_table[now_statu].action_callback(&next_state)){
                    state_machine->now_state = next_state;
                    _clear_state_machine_rxd_buffer(state_machine);
                    return;
                }
            }
        }
    }

    // 超时处理
    if((state_machine->state_table[now_statu].onece_time_out != NO_TIMEOUT) &&
       (GET_MS_TICK() - state_machine->state_table[now_statu].time_temp >=
        state_machine->state_table[now_statu].onece_time_out)){
        state_machine->state_table[now_statu].time_temp = 0;
        state_machine->state_table[now_statu].re_try_times_temp++;
    }
}
```

## 6. 用户界面模块

### 6.1 LVGL移植 (lv_port_disp.c)

```c
// 显示接口初始化
void lv_port_disp_init(void)
{
    static lv_disp_draw_buf_t draw_buf_dsc_1;
    static lv_color_t draw_buf_1[MY_DISP_HOR_RES * 10];
    lv_disp_draw_buf_init(&draw_buf_dsc_1, draw_buf_1, NULL, MY_DISP_HOR_RES * 10);

    static lv_disp_drv_t disp_drv;
    lv_disp_drv_init(&disp_drv);
    disp_drv.hor_res = MY_DISP_HOR_RES;
    disp_drv.ver_res = MY_DISP_VER_RES;
    disp_drv.flush_cb = disp_flush;
    disp_drv.draw_buf = &draw_buf_dsc_1;
    lv_disp_drv_register(&disp_drv);
}

// 显示刷新回调
static void disp_flush(lv_disp_drv_t * disp_drv, const lv_area_t * area, lv_color_t * color_p)
{
    pm_fill_area(area->x1, area->y1, area->x2, area->y2, (uint8_t*)color_p);
    lv_disp_flush_ready(disp_drv);
}
```

### 6.2 触摸输入接口 (lv_port_indev.c)

```c
// 输入设备初始化
void lv_port_indev_init(void)
{
    touchpad_init();

    static lv_indev_drv_t indev_drv;
    lv_indev_drv_init(&indev_drv);
    indev_drv.type = LV_INDEV_TYPE_POINTER;
    indev_drv.read_cb = pm_tp_point_read;
    indev_touchpad = lv_indev_drv_register(&indev_drv);
}

// 触摸点读取
void pm_tp_point_read(lv_indev_drv_t * indev_drv, lv_indev_data_t * data)
{
    uint16_t x, y;
    uint8_t state;
    chsc5816_tp_point_read(&x, &y, &state);
    data->point.x = x;
    data->point.y = y;
    data->state = (state) ? LV_INDEV_STATE_PRESSED : LV_INDEV_STATE_RELEASED;
    pm_touch_state = state;
}
```

### 6.3 UI界面管理 (ui.c)

```c
// UI初始化
void ui_init(void)
{
    lv_disp_t * dispp = lv_disp_get_default();
    lv_theme_t * theme = lv_theme_default_init(dispp,
                                               lv_palette_main(LV_PALETTE_BLUE),
                                               lv_palette_main(LV_PALETTE_RED),
                                               true, LV_FONT_DEFAULT);
    lv_disp_set_theme(dispp, theme);
    ui_StartScreen_screen_init();
    lv_disp_load_scr(ui_StartScreen);
}

// 主界面事件处理
void ui_event_HomeScreen(lv_event_t * e)
{
    lv_event_code_t event_code = lv_event_get_code(e);
    lv_obj_t * target = lv_event_get_target(e);

    if(event_code == LV_EVENT_GESTURE &&
       lv_indev_get_gesture_dir(lv_indev_get_act()) == LV_DIR_LEFT) {
        lv_indev_wait_release(lv_indev_get_act());
        lv_obj_del_async(ui_HomeScreen);
        ui_SysSetingScreen_screen_init();
        _ui_screen_change(&ui_SysSetingScreen, LV_SCR_LOAD_ANIM_NONE, 0, 0,
                         &ui_SysSetingScreen_screen_init);
    }
}

// 主界面初始化
void ui_HomeScreen_screen_init(void)
{
    beijing_time_t t;
    get_beijing_time(&t, sys_unix_time);

    ui_HomeScreen = lv_obj_create(NULL);
    lv_obj_clear_flag(ui_HomeScreen, LV_OBJ_FLAG_SCROLLABLE);

    // 时间显示面板
    ui_TimePanel = lv_obj_create(ui_HomeScreen);
    lv_obj_set_width(ui_TimePanel, 320);
    lv_obj_set_height(ui_TimePanel, 80);
    lv_obj_set_align(ui_TimePanel, LV_ALIGN_TOP_MID);

    // 小时标签
    ui_HourLable = lv_label_create(ui_TimePanel);
    lv_obj_set_align(ui_HourLable, LV_ALIGN_LEFT_MID);
    lv_label_set_text_fmt(ui_HourLable, "%02d", t.hour);
    lv_obj_set_style_text_font(ui_HourLable, &ui_font_pjfont70, LV_PART_MAIN | LV_STATE_DEFAULT);

    // 分钟标签
    ui_MinuLable = lv_label_create(ui_TimePanel);
    lv_obj_set_align(ui_MinuLable, LV_ALIGN_RIGHT_MID);
    lv_label_set_text_fmt(ui_MinuLable, "%02d", t.minute);
    lv_obj_set_style_text_font(ui_MinuLable, &ui_font_pjfont70, LV_PART_MAIN | LV_STATE_DEFAULT);

    // 剂量率显示面板
    ui_DoseRatePanel = lv_obj_create(ui_HomeScreen);
    lv_obj_set_width(ui_DoseRatePanel, 320);
    lv_obj_set_height(ui_DoseRatePanel, 71);
    lv_obj_set_align(ui_DoseRatePanel, LV_ALIGN_CENTER);

    ui_DoseRateLable = lv_label_create(ui_DoseRatePanel);
    lv_obj_set_align(ui_DoseRateLable, LV_ALIGN_CENTER);
    char dose_rate_string[20];
    sprintf(dose_rate_string,"%0.2f", csi_sensor.dose_rate);
    lv_label_set_text(ui_DoseRateLable, dose_rate_string);
    lv_obj_set_style_text_color(ui_DoseRateLable, lv_color_hex(0xFFB600), LV_PART_MAIN | LV_STATE_DEFAULT);
    lv_obj_set_style_text_font(ui_DoseRateLable, &ui_font_pjXiFont40, LV_PART_MAIN | LV_STATE_DEFAULT);

    // 添加事件处理
    lv_obj_add_event_cb(ui_HomeScreen, ui_event_HomeScreen, LV_EVENT_ALL, NULL);
}
```

### 6.4 显示数据更新 (user_display.c)

```c
// 显示数据更新函数
void user_display_updata(void)
{
    static uint32_t mp_t = 0;

    // 开机画面切换逻辑
    if(system_state.watch_power_state == 2){
        if(get_userkey()==0){              // 等待开机键释放后再切换画面
            return;
        }
        ui_HomeScreen_screen_init();
        _ui_screen_change(&ui_HomeScreen, LV_SCR_LOAD_ANIM_NONE, 0, 1000, &ui_HomeScreen_screen_init);
        system_state.watch_power_state = 3;
    }

    // 主界面数据更新
    if(lv_scr_act() == ui_HomeScreen){
        update_home_screen_data();
    }

    // 设置界面数据更新
    if(lv_scr_act() == ui_SysSetingScreen){
        update_setting_screen_data();
    }
}

// 主界面数据更新
void update_home_screen_data(void)
{
    // 更新时间显示
    static uint32_t last_minute = 0xFFFFFFFF;
    beijing_time_t t;
    get_beijing_time(&t, sys_unix_time);
    uint32_t now_minute = t.hour * 60 + t.minute;

    if(last_minute != now_minute){
        last_minute = now_minute;
        lv_label_set_text_fmt(ui_HourLable, "%02d", t.hour);
        lv_label_set_text_fmt(ui_MinuLable, "%02d", t.minute);
        lv_label_set_text_fmt(ui_DateLable, "%02d/%02d", t.month, t.day);
    }

    // 更新剂量率显示
    static float last_dose_rate = 9999999.66f;
    if(last_dose_rate != csi_sensor.dose_rate){
        last_dose_rate = csi_sensor.dose_rate;
        char dose_rate_string[20];
        sprintf(dose_rate_string,"%0.2f", csi_sensor.dose_rate);
        lv_label_set_text(ui_DoseRateLable, dose_rate_string);
    }

    // 更新电量显示
    static uint8_t last_bt_prt = 0;
    if(dev_battery_prt != last_bt_prt){
        last_bt_prt = dev_battery_prt;
        lv_label_set_text_fmt(ui_BatLable, "%d%%", last_bt_prt);
        lv_color_t bt_color = last_bt_prt > 10 ? lv_color_hex(0x24B600) : lv_color_hex(0xFF0000);
        lv_obj_set_style_text_color(ui_BatLable, bt_color, LV_PART_MAIN | LV_STATE_DEFAULT);
    }
}
```

## 7. 数据协议模块

### 7.1 Protobuf数据结构 (wb01_message.proto)

```protobuf
// 上报数据点
message data_point_t{
    required string sn = 1 [(nanopb).max_length = 13];              // 设备序列号
    required string firmware_version = 2 [(nanopb).max_length = 17]; // 固件版本号
    required string param_hash = 3 [(nanopb).max_length = 65];       // 参数哈希值
    required uint64 timestamp = 4;                                   // Unix时间戳

    optional dtu_t dtu = 5;                                          // DTU信息
    optional gnss_t gnss = 6;                                        // GNSS定位信息
    optional dose_rate_t dose_rate = 7;                              // 剂量率信息
    optional power_battery_t power_battery = 8;                      // 电池信息
}

// 剂量率数据
message dose_rate_t{
    required float dose_rate = 1;                                    // 当前剂量率
    required float dose_sum = 2;                                     // 累计剂量
    required float cps = 3;                                          // 计数率
    required uint32 cnt = 4;                                         // 脉冲计数
    required uint32 alarm = 5;                                       // 报警状态
}

// GNSS定位数据
message gnss_t{
    required double latitude = 1;                                    // 纬度
    required double longitude = 2;                                   // 经度
    required uint32 altitude = 3;                                    // 海拔
    required uint32 star_num = 4;                                    // 卫星数量
}

// DTU通信模块信息
message dtu_t{
    required string imei = 1 [(nanopb).max_length = 16];            // 设备IMEI
    required string iccid = 2 [(nanopb).max_length = 21];           // SIM卡ICCID
    required uint32 lac = 3;                                         // 位置区码
    required uint32 cid = 4;                                         // 小区ID
    required uint32 csq = 5;                                         // 信号强度
}

// 电池电源信息
message power_battery_t{
    required uint32 battery_prt = 1;                                 // 电池电量百分比
    required uint32 charger_state = 2;                              // 充电器状态
}
```

### 7.2 数据编码处理 (analytical_pb.c)

```c
// 编码数据点信息
uint16_t encode_data_point(uint8_t * output_buffer, uint16_t buffer_size)
{
    wb01_message_data_point_t data_point = wb01_message_data_point_t_init_zero;

    // 填充基本信息
    strcpy(data_point.sn, system_setings.sn);
    strcpy(data_point.firmware_version, FIRMWARE_VERSION);
    strcpy(data_point.param_hash, system_setings.param_hash);
    data_point.timestamp = sys_unix_time;

    // 填充DTU信息
    data_point.has_dtu = true;
    strcpy(data_point.dtu.imei, (char*)default_ec800m.imei);
    strcpy(data_point.dtu.iccid, (char*)default_ec800m.iccid);
    data_point.dtu.lac = default_ec800m.lac;
    data_point.dtu.cid = default_ec800m.cid;
    data_point.dtu.csq = default_ec800m.csq;

    // 填充GNSS信息
    data_point.has_gnss = true;
    data_point.gnss.latitude = default_ec800m.gnss_latitude;
    data_point.gnss.longitude = default_ec800m.gnss_longitude;
    data_point.gnss.altitude = default_ec800m.gnss_altitude;
    data_point.gnss.star_num = default_ec800m.gnss_star_num;

    // 填充剂量率信息
    data_point.has_dose_rate = true;
    data_point.dose_rate.dose_rate = csi_sensor.dose_rate;
    data_point.dose_rate.dose_sum = csi_sensor.dose_sum;
    data_point.dose_rate.cps = csi_sensor.cps;
    data_point.dose_rate.cnt = csi_sensor.cnt_now;
    data_point.dose_rate.alarm = csi_sensor.alarm;

    // 填充电池信息
    data_point.has_power_battery = true;
    data_point.power_battery.battery_prt = dev_battery_prt;
    data_point.power_battery.charger_state = charger_state;

    // 编码
    pb_ostream_t stream = pb_ostream_from_buffer(output_buffer, buffer_size);
    bool status = pb_encode(&stream, wb01_message_data_point_t_fields, &data_point);

    if(!status){
        return 0;
    }

    return stream.bytes_written;
}

// 数据上传检查
void check_upsend_envent(void)
{
    static unix_time_t last_check_time = 0;

    if(sys_unix_time - last_check_time >= 10){  // 每10秒检查一次
        last_check_time = sys_unix_time;

        // 检查是否需要上传数据
        if(system_setings.auto_upsend_switch){
            unix_time_t interval = system_setings.auto_upsend_interval_normal;

            // 如果有报警事件，使用短间隔
            if(csi_sensor.alarm != 0){
                interval = system_setings.auto_upsend_interval_envent;
            }

            if((sys_unix_time - default_ec800m.last_upsend_time) >= interval){
                // 编码数据
                uint8_t pb_buffer[512];
                uint16_t pb_length = encode_data_point(pb_buffer, sizeof(pb_buffer));

                if(pb_length > 0){
                    default_ec800m.new_data = pb_buffer;
                    default_ec800m.new_data_length = pb_length;
                    default_ec800m.new_data_redy = 1;
                    default_ec800m.last_upsend_time = sys_unix_time;
                }
            }
        }
    }
}
```

## 8. 外设驱动模块

### 8.1 显示屏驱动 (icna3311.c)

```c
// 显示屏初始化命令
void icna3311_init_cmd(uint16_t start_column, uint16_t end_column,
                       uint16_t start_row, uint16_t end_row)
{
    // 软复位
    icna3311_write_4wspi(0x01, NULL, 0);
    DelayMs(120);

    // 退出睡眠模式
    icna3311_write_4wspi(0x11, NULL, 0);
    DelayMs(120);

    // 设置显示区域
    uint8_t column_data[4] = {start_column >> 8, start_column & 0xFF,
                              end_column >> 8, end_column & 0xFF};
    icna3311_write_4wspi(0x2A, column_data, 4);

    uint8_t row_data[4] = {start_row >> 8, start_row & 0xFF,
                           end_row >> 8, end_row & 0xFF};
    icna3311_write_4wspi(0x2B, row_data, 4);

    // 开启显示
    icna3311_write_4wspi(0x29, NULL, 0);
    DelayMs(50);
}

// SPI写入函数
void icna3311_write_4wspi(uint8_t cmd, uint8_t * data, uint16_t data_len)
{
    ICNA3311_CS_0;                       // 片选拉低

    ICNA3311_DC_0;                       // 命令模式
    SPI1_MasterSendByte(cmd);            // 发送命令

    if(data_len > 0){
        ICNA3311_DC_1;                   // 数据模式
        for(uint16_t i = 0; i < data_len; i++){
            SPI1_MasterSendByte(data[i]); // 发送数据
        }
    }

    ICNA3311_CS_1;                       // 片选拉高
}

// 区域填充函数
void pm_fill_area(uint16_t x1, uint16_t y1, uint16_t x2, uint16_t y2, uint8_t * color_data)
{
    uint32_t pixel_count = (x2 - x1 + 1) * (y2 - y1 + 1);

    // 设置显示窗口
    uint8_t column_data[4] = {x1 >> 8, x1 & 0xFF, x2 >> 8, x2 & 0xFF};
    icna3311_write_4wspi(0x2A, column_data, 4);

    uint8_t row_data[4] = {y1 >> 8, y1 & 0xFF, y2 >> 8, y2 & 0xFF};
    icna3311_write_4wspi(0x2B, row_data, 4);

    // 开始写入像素数据
    ICNA3311_CS_0;
    ICNA3311_DC_0;
    SPI1_MasterSendByte(0x2C);           // 写入内存命令

    ICNA3311_DC_1;                       // 数据模式
    for(uint32_t i = 0; i < pixel_count; i++){
        SPI1_MasterSendByte(color_data[i]);
    }

    ICNA3311_CS_1;
}
```

### 8.2 触摸屏驱动 (chsc5816.c)

```c
// 触摸点读取
void chsc5816_tp_point_read(uint16_t *tp_x, uint16_t *tp_y, uint8_t *tp_state)
{
    rpt_content_t tp;
    memset(&tp, 0, sizeof(rpt_content_t));

    if(chsc5816_read(0x2000002C, (uint8_t*)&tp, sizeof(rpt_content_t)) == 0){
        if(tp.act == 1 && tp.num > 0){   // 有触摸点
            *tp_x = tp.points[0].rp.x;
            *tp_y = tp.points[0].rp.y;
            *tp_state = 1;               // 触摸状态
        }
        else{
            *tp_state = 0;               // 释放状态
        }
    }
    else{
        *tp_state = 0;
    }
}

// I2C读取函数
uint8_t chsc5816_read(uint32_t addr, uint8_t * buf, uint16_t len)
{
    uint8_t addr_buf[4];
    addr_buf[0] = (addr >> 24) & 0xFF;
    addr_buf[1] = (addr >> 16) & 0xFF;
    addr_buf[2] = (addr >> 8) & 0xFF;
    addr_buf[3] = addr & 0xFF;

    return io_i2c_dev_read(CHSC5816_I2C_ADDR_7BIT, addr_buf[0], 4, buf, len);
}

// 触摸中断处理
void chsc5816_tp_interrupt_handler(void)
{
    if(GPIOB_ReadITFlagBit(GPIO_Pin_15)){
        GPIOB_ClearITFlagBit(GPIO_Pin_15);
        // 触摸中断处理逻辑
        // 可以在这里设置标志位，在主循环中处理触摸事件
    }
}
```

### 8.3 心率血氧传感器驱动 (ob1203.c)

```c
// OB1203读取函数
uint8_t ob1203_read(uint8_t addr, uint8_t *buf, uint8_t len)
{
    return io_i2c_dev_read(OB1203_I2C_ADDR_7BIT, addr, 1, buf, len);
}

// OB1203写入函数
uint8_t ob1203_write(uint8_t addr, uint8_t * buf, uint8_t len)
{
    return io_i2c_dev_write(OB1203_I2C_ADDR_7BIT, addr, 1, buf, len);
}

// 心率血氧初始化
uint8_t ob1203_init(void)
{
    uint8_t init_data;

    // 软复位
    init_data = 0x08;
    if(ob1203_write(0x00, &init_data, 1) != 0) return 1;
    DelayMs(10);

    // 配置LED驱动电流
    init_data = 0x3F;  // 最大电流
    if(ob1203_write(0x15, &init_data, 1) != 0) return 1;

    // 配置采样率
    init_data = 0x00;  // 25Hz采样率
    if(ob1203_write(0x11, &init_data, 1) != 0) return 1;

    // 启动心率模式
    init_data = 0x02;
    if(ob1203_write(0x10, &init_data, 1) != 0) return 1;

    return 0;
}

// 读取心率数据
uint8_t ob1203_read_heart_rate(uint16_t *heart_rate, uint8_t *spo2)
{
    uint8_t status;
    uint8_t data_buf[6];

    // 读取状态寄存器
    if(ob1203_read(0x00, &status, 1) != 0) return 1;

    if(status & 0x01){  // 数据就绪
        // 读取心率和血氧数据
        if(ob1203_read(0x1C, data_buf, 6) != 0) return 1;

        *heart_rate = (data_buf[1] << 8) | data_buf[0];
        *spo2 = data_buf[5];

        return 0;
    }

    return 1;  // 数据未就绪
}
```

### 8.4 SPI Flash驱动 (gd25lq32eeigr.c)

```c
// SPI片选控制
void spi_cs(uint8_t bit_statu)
{
    switch (bit_statu)
    {
        case 0:{
            GPIOA_ResetBits(GPIO_Pin_2);    // 片选拉低
            GPIOA_ModeCfg(GPIO_Pin_2, GPIO_ModeOut_PP_5mA);
        }
        break;

        default:{
            GPIOA_ModeCfg(GPIO_Pin_2, GPIO_ModeIN_Floating);  // 片选拉高
        }
        break;
    }
}

// 读取Flash ID
uint32_t spi_flash_read_id(void)
{
    uint32_t flash_id = 0;
    uint8_t id_buf[3];

    spi_cs(0);                           // 片选拉低
    SPI1_MasterSendByte(CMD_READ_ID);    // 发送读ID命令
    id_buf[0] = SPI1_MasterSendByte(0xFF);
    id_buf[1] = SPI1_MasterSendByte(0xFF);
    id_buf[2] = SPI1_MasterSendByte(0xFF);
    spi_cs(1);                           // 片选拉高

    flash_id = (id_buf[0] << 16) | (id_buf[1] << 8) | id_buf[2];
    return flash_id;
}

// 读取Flash数据
void spi_flash_read_data(uint32_t addr, uint8_t * buf, uint16_t len)
{
    spi_cs(0);
    SPI1_MasterSendByte(0x03);           // 读命令
    SPI1_MasterSendByte((addr >> 16) & 0xFF);
    SPI1_MasterSendByte((addr >> 8) & 0xFF);
    SPI1_MasterSendByte(addr & 0xFF);

    for(uint16_t i = 0; i < len; i++){
        buf[i] = SPI1_MasterSendByte(0xFF);
    }

    spi_cs(1);
}

// 写入Flash数据
void spi_flash_write_data(uint32_t addr, uint8_t * buf, uint16_t len)
{
    // 写使能
    spi_cs(0);
    SPI1_MasterSendByte(0x06);           // 写使能命令
    spi_cs(1);

    // 页编程
    spi_cs(0);
    SPI1_MasterSendByte(0x02);           // 页编程命令
    SPI1_MasterSendByte((addr >> 16) & 0xFF);
    SPI1_MasterSendByte((addr >> 8) & 0xFF);
    SPI1_MasterSendByte(addr & 0xFF);

    for(uint16_t i = 0; i < len; i++){
        SPI1_MasterSendByte(buf[i]);
    }

    spi_cs(1);

    // 等待写入完成
    uint8_t status;
    do {
        DelayMs(1);
        spi_cs(0);
        SPI1_MasterSendByte(0x05);       // 读状态寄存器命令
        status = SPI1_MasterSendByte(0xFF);
        spi_cs(1);
    } while(status & 0x01);              // 等待WIP位清零
}
```

## 9. 系统特性总结

### 9.1 核心功能特点

1. **辐射剂量检测**:
   - 基于CSI传感器的γ射线检测
   - 实时剂量率计算和累计剂量统计
   - 智能跳变检测算法，提高测量精度
   - 分段拟合算法，适应不同计数率区间

2. **4G通信功能**:
   - EC800M模块实现4G网络连接
   - MQTT协议数据上传
   - AT状态机管理通信流程
   - 支持GNSS定位功能

3. **用户界面**:
   - 基于LVGL的图形界面
   - 触摸屏交互支持
   - 多界面切换和手势操作
   - 实时数据显示更新

4. **数据管理**:
   - Protobuf协议数据编码
   - SPI Flash数据存储
   - 系统配置管理
   - 数据校验和备份机制

5. **电源管理**:
   - 电池电量监测
   - 充电状态检测
   - 低功耗设计
   - 智能电源管理

### 9.2 技术亮点

1. **跳变检测算法**:
   - 采用3σ统计方法和连续增减法
   - 有效识别辐射环境突变
   - 提高测量数据的可靠性

2. **分段拟合算法**:
   - 针对不同计数率区间使用不同的拟合参数
   - 二次拟合公式: dose_rate = a*x² + b*x + c
   - 提高测量精度和线性度

3. **AT状态机**:
   - 灵活的状态机框架
   - 支持超时重试机制
   - 便于扩展和维护

4. **模块化设计**:
   - 良好的代码结构
   - 各功能模块独立
   - 便于开发和调试

### 9.3 系统架构优势

1. **实时性**:
   - 中断驱动的设计保证了系统的实时响应
   - 1ms定时器中断提供精确时基
   - 秒中断处理剂量率计算

2. **稳定性**:
   - 看门狗机制防止系统死机
   - 错误处理和重试机制
   - 数据校验和备份

3. **可扩展性**:
   - 模块化设计便于功能扩展
   - 标准化的接口设计
   - 支持多种传感器接入

4. **可维护性**:
   - 清晰的代码结构和注释
   - 统一的编程风格
   - 完善的调试接口

### 9.4 关键技术参数

- **MCU**: CH579 32位ARM Cortex-M0内核
- **通信**: EC800M 4G模块，支持Cat.1网络
- **显示**: 320x240 TFT LCD，支持触摸
- **存储**: 4MB SPI Flash
- **传感器**: CSI辐射传感器、OB1203心率血氧传感器
- **电池**: 锂电池，支持磁吸充电
- **工作温度**: -10°C ~ +60°C
- **防护等级**: IP65

---

**文档说明**: 本文档详细整理了智能手表项目的核心代码模块，涵盖了主程序、驱动程序、通信模块、用户界面、数据协议等所有关键部分。每个模块都提供了详细的代码示例和功能说明，并标注了重要的技术要点，便于理解项目的整体架构和实现细节。该文档可作为项目开发、维护和技术交流的重要参考资料。

